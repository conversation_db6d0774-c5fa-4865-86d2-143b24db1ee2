import { expect } from "@playwright/test";
import { test } from "@core/fixture/fixture-api";
import ApplicationRequestsAPI from "@api/application-requests/application-requests.api";
import testData from "@tests-data/application-requests-data.json";
import {
    CreateApplicationRequest,
    UpdateApplicationRequest,
    GetApplicationRequestsParams,
    ApplicationRequestResponse,
    ApplicationRequestDetailResponse
} from "src/data-type/application-request.type";
// Define response type interface locally
interface ApiResponseType {
    data: any;
    isSuccess: boolean;
    statusCode: number;
    errors: any[];
}

test.describe('Application Requests API Tests', () => {
    let applicationRequestsAPI: ApplicationRequestsAPI;

    test.beforeEach(async ({ request }) => {
        applicationRequestsAPI = new ApplicationRequestsAPI(request);
    });

    test.describe('GET /api/application-requests/current-user', () => {
        test('AR_001 - Should get current user application request successfully', async () => {
            let response: any;
            let responseBody: ApiResponseType;

            await test.step('Send GET request to get current user application', async () => {
                response = await applicationRequestsAPI.getCurrentUserApplicationRequest();
                responseBody = await response.json();
            });

            await test.step('Verify successful response', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                // Verify response structure if application exists
                if (responseBody.data) {
                    expect(responseBody.data).toHaveProperty('id');
                    expect(responseBody.data).toHaveProperty('education');
                    expect(responseBody.data).toHaveProperty('workExperience');
                    expect(responseBody.data).toHaveProperty('description');
                    expect(responseBody.data).toHaveProperty('status');
                }
            });
        });
    });

    test.describe('POST /api/application-requests', () => {
        test('AR_002 - Should create application request successfully with valid data', async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const requestData: CreateApplicationRequest = testData.createRequest.validData;

            await test.step('Send POST request to create application', async () => {
                response = await applicationRequestsAPI.createApplicationRequest(requestData);
                responseBody = await response.json();
            });

            await test.step('Verify successful creation', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                // Verify created application data if available
                if (responseBody.data) {
                    expect(responseBody.data.education).toBe(requestData.Education);
                    expect(responseBody.data.workExperience).toBe(requestData.WorkExperience);
                    expect(responseBody.data.description).toBe(requestData.Description);
                }
            });
        });

        testData.createRequest.invalidData.forEach((testCase, index) => {
            test(`AR_003_${index + 1} - Should fail to create application: ${testCase.testCase}`, async () => {
                let response: any;
                let responseBody: ApiResponseType;

                await test.step(`Send POST request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.createApplicationRequest(testCase.data as CreateApplicationRequest);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    expect(responseBody.isSuccess).toBeFalsy();
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('PUT /api/application-requests', () => {
        test('AR_004 - Should update application request successfully with valid data', async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const updateData: UpdateApplicationRequest = testData.updateRequest.validData;

            await test.step('Send PUT request to update application', async () => {
                response = await applicationRequestsAPI.updateApplicationRequest(updateData);
                responseBody = await response.json();
            });

            await test.step('Verify successful update', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
            });
        });

        testData.updateRequest.invalidData.forEach((testCase, index) => {
            test(`AR_005_${index + 1} - Should fail to update application: ${testCase.testCase}`, async () => {
                let response: any;
                let responseBody: ApiResponseType;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.updateApplicationRequest(testCase.data as UpdateApplicationRequest);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    expect(responseBody.isSuccess).toBeFalsy();
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('GET /api/application-requests', () => {
        testData.getRequests.validParams.forEach((testCase, index) => {
            test(`AR_006_${index + 1} - Should get application requests: ${testCase.testCase}`, async () => {
                let response: any;
                let responseBody: ApiResponseType;

                await test.step(`Send GET request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.getApplicationRequests(testCase.params as GetApplicationRequestsParams);
                    responseBody = await response.json();
                });

                await test.step('Verify successful response', async () => {
                    expect(response.status()).toBe(200);
                    expect(responseBody.statusCode).toBe(200);
                    expect(responseBody.isSuccess).toBeTruthy();
                    expect(responseBody.data).toBeDefined();
                    expect(responseBody.data.items).toBeDefined();
                    expect(Array.isArray(responseBody.data.items)).toBeTruthy();
                    expect(responseBody.data.totalCount).toBeDefined();
                    expect(responseBody.data.pageNumber).toBe(testCase.params.PageNumber);
                    expect(responseBody.data.pageSize).toBe(testCase.params.PageSize);

                    // Verify item structure if items exist
                    if (responseBody.data.items.length > 0) {
                        const item = responseBody.data.items[0] as ApplicationRequestResponse;
                        expect(item).toHaveProperty('id');
                        expect(item).toHaveProperty('education');
                        expect(item).toHaveProperty('workExperience');
                        expect(item).toHaveProperty('fullName');
                        expect(item).toHaveProperty('description');
                        expect(item).toHaveProperty('status');
                        expect(item).toHaveProperty('summitted');
                    }
                });
            });
        });
    });

    test.describe('GET /api/application-requests/{id}', () => {
        test('AR_007 - Should get application request by ID successfully', async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const validId = testData.testIds.validId;

            await test.step('Send GET request to get application by ID', async () => {
                response = await applicationRequestsAPI.getApplicationRequestById(validId);
                responseBody = await response.json();
            });

            await test.step('Verify successful response', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.data).toBeDefined();

                const applicationDetail = responseBody.data as ApplicationRequestDetailResponse;
                expect(applicationDetail).toHaveProperty('id');
                expect(applicationDetail).toHaveProperty('education');
                expect(applicationDetail).toHaveProperty('workExperience');
                expect(applicationDetail).toHaveProperty('fullName');
                expect(applicationDetail).toHaveProperty('description');
                expect(applicationDetail).toHaveProperty('status');
                expect(applicationDetail).toHaveProperty('summitted');
                expect(applicationDetail).toHaveProperty('note');
                expect(applicationDetail).toHaveProperty('applicationRequestDocuments');
                expect(applicationDetail).toHaveProperty('mentorEmail');
                expect(applicationDetail).toHaveProperty('mentorExpertises');
                expect(applicationDetail).toHaveProperty('mentorCertifications');
                expect(applicationDetail).toHaveProperty('avatarUrl');
            });
        });

        test('AR_008 - Should return 404 for non-existent application ID', async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const invalidId = testData.testIds.invalidId;

            await test.step('Send GET request with invalid ID', async () => {
                response = await applicationRequestsAPI.getApplicationRequestById(invalidId);
                responseBody = await response.json();
            });

            await test.step('Verify 404 response', async () => {
                expect(response.status()).toBe(404);
                expect(responseBody.isSuccess).toBeFalsy();
                if (responseBody.errors && responseBody.errors.length > 0) {
                    expect(responseBody.errors[0].message).toContain('Application request not found');
                }
            });
        });
    });

    test.describe('PUT /api/application-requests/{id}/request-update', () => {
        test('AR_009 - Should request update successfully with valid data', async () => {
            let response: any;
            let responseBody: ApiResponseType;
            const validId = testData.testIds.validId;
            const requestData = testData.requestUpdate.validData;

            await test.step('Send PUT request to request update', async () => {
                response = await applicationRequestsAPI.requestUpdate(validId, requestData);
                responseBody = await response.json();
            });

            await test.step('Verify successful request update', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
            });
        });

        testData.requestUpdate.invalidData.forEach((testCase, index) => {
            test(`AR_010_${index + 1} - Should fail to request update: ${testCase.testCase}`, async () => {
                let response: any;
                let responseBody: any;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.requestUpdate(testCase.id, testCase.data);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('PUT /api/application-requests/{id}/approve', () => {
        test('AR_011 - Should approve application request successfully', async () => {
            let response: any;
            let responseBody: any;
            const validId = testData.approveRequest.validId;

            await test.step('Send PUT request to approve application', async () => {
                response = await applicationRequestsAPI.approveRequest(validId);
                responseBody = await response.json();
            });

            await test.step('Verify successful approval', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.message).toBe(testData.expectedResponses.approveSuccess);
            });
        });

        testData.approveRequest.invalidData.forEach((testCase, index) => {
            test(`AR_012_${index + 1} - Should fail to approve application: ${testCase.testCase}`, async () => {
                let response: any;
                let responseBody: any;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.approveRequest(testCase.id);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('PUT /api/application-requests/{id}/reject', () => {
        test('AR_013 - Should reject application request successfully with valid data', async () => {
            let response: any;
            let responseBody: any;
            const validId = testData.testIds.validId;
            const rejectData = testData.rejectRequest.validData;

            await test.step('Send PUT request to reject application', async () => {
                response = await applicationRequestsAPI.rejectRequest(validId, rejectData);
                responseBody = await response.json();
            });

            await test.step('Verify successful rejection', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.message).toBe(testData.expectedResponses.rejectSuccess);
            });
        });

        testData.rejectRequest.invalidData.forEach((testCase, index) => {
            test(`AR_014_${index + 1} - Should fail to reject application: ${testCase.testCase}`, async () => {
                let response: any;
                let responseBody: any;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.rejectRequest(testCase.id, testCase.data);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('Integration Tests', () => {
        test('AR_015 - Complete application lifecycle test', async () => {
            let createResponse: any;
            let createResponseBody: any;
            let applicationId: string;
            const createData: CreateApplicationRequest = testData.createRequest.validData;

            await test.step('Create new application request', async () => {
                createResponse = await applicationRequestsAPI.createApplicationRequest(createData);
                createResponseBody = await createResponse.json();
                expect(createResponse.status()).toBe(200);
                applicationId = createResponseBody.data?.id;
            });

            if (applicationId) {
                await test.step('Get created application by ID', async () => {
                    const getResponse = await applicationRequestsAPI.getApplicationRequestById(applicationId);
                    const getResponseBody = await getResponse.json();
                    expect(getResponse.status()).toBe(200);
                    expect(getResponseBody.data.id).toBe(applicationId);
                });

                await test.step('Update the application', async () => {
                    const updateData: UpdateApplicationRequest = {
                        ...testData.updateRequest.validData,
                        Id: applicationId
                    };
                    const updateResponse = await applicationRequestsAPI.updateApplicationRequest(updateData);
                    expect(updateResponse.status()).toBe(200);
                });

                await test.step('Request update from mentor', async () => {
                    const requestUpdateResponse = await applicationRequestsAPI.requestUpdate(
                        applicationId,
                        testData.requestUpdate.validData
                    );
                    expect(requestUpdateResponse.status()).toBe(200);
                });

                await test.step('Finally approve the application', async () => {
                    const approveResponse = await applicationRequestsAPI.approveRequest(applicationId);
                    expect(approveResponse.status()).toBe(200);
                });
            }
        });
    });
});