import { expect, test as baseTest } from "@playwright/test";
import ApplicationRequestsAPI from "@api/application-requests/application-requests.api";
import RegisterAP<PERSON> from "@api/register/register.api";
import LoginLogoutAP<PERSON> from "@api/login-logout/login-logout.api";
import VerifyEmail<PERSON><PERSON> from "@api/verify-email/verify-email.api";
import testData from "@tests-data/application-requests-data.json";
import {
    CreateApplicationRequest,
    UpdateApplicationRequest,
    GetApplicationRequestsParams,
    ApplicationRequestResponse,
    ApplicationRequestDetailResponse
} from "src/data-type/application-request.type";
import { RegisterUserRequest } from "src/data-type/register-user.type";

// Define response type interface locally
interface ApiResponseType {
    data: any;
    isSuccess: boolean;
    statusCode: number;
    errors: any[];
}

// Define test fixture type
type TestFixtures = {
    mentorCredentials: {
        email: string;
        password: string;
        accessToken: string;
    };
    applicationRequestsAPI: ApplicationRequestsAPI;
};

// Helper function to prompt for user input
function promptUser(question: string): Promise<string> {
    return new Promise((resolve) => {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        rl.question(question, (answer: string) => {
            rl.close();
            resolve(answer.trim());
        });
    });
}

// Extend base test with fixtures
const test = baseTest.extend<TestFixtures>({
    mentorCredentials: async ({ request }, use) => {
        console.log('\n=== MENTOR REGISTRATION PROCESS ===');

        // Step 1: Get email and password from user
        const email = await promptUser('Enter email from 10minutemail: ');
        const password = await promptUser('Enter password (e.g., MentorTest123@): ');

        console.log(`Using email: ${email}`);
        console.log(`Using password: ${password}`);

        // Step 2: Try login first to see if account already exists
        const loginAPI = new LoginLogoutAPI(request);
        const loginResponse = await loginAPI.login(email, password);

        if (loginResponse.status() === 200) {
            console.log('✅ Account already exists and login successful!');
            const loginBody = await loginResponse.json();
            const accessToken = loginBody.data.accessToken;

            await use({
                email,
                password,
                accessToken
            });
            return;
        }

        console.log('❌ Login failed, proceeding with registration...');

        // Step 3: Register new mentor account
        const registerAPI = new RegisterAPI(request);
        const mentorData: RegisterUserRequest = {
            Email: email,
            Password: password,
            FullName: "Test Mentor API",
            Role: 1, // Mentor role
            Bio: "Test mentor for application requests API testing",
            Expertises: ["8a5bc300-21c4-47d0-bb33-27d0a709d417"],
            ProfessionalSkill: "Software Development",
            Experience: "5 years of experience in software development",
            CommunicationPreference: 1,
            Goals: "Help others learn programming and improve technical skills",
            CourseCategoryIds: ["f47ac10b-58cc-4372-a567-0e02b2c3d479"],
            SessionFrequency: 2,
            Duration: 60,
            LearningStyle: 1,
            TeachingStyles: [1]
        };

        console.log('📧 Sending registration request...');
        const registerResponse = await registerAPI.register(mentorData);
        console.log(`Register response status: ${registerResponse.status()}`);

        if (registerResponse.status() !== 200) {
            const errorBody = await registerResponse.text();
            console.error('Registration failed:', errorBody);
            throw new Error(`Registration failed with status ${registerResponse.status()}`);
        }

        console.log('✅ Registration request sent successfully!');
        console.log('📧 Please check your email for verification code...');

        // Step 4: Get verification code from user
        const verificationCode = await promptUser('Enter verification code from email: ');
        console.log(`Using verification code: ${verificationCode}`);

        // Step 5: Verify email with code using VerifyEmailAPI
        console.log('📧 Verifying email with code...');
        const verifyEmailAPI = new VerifyEmailAPI(request);
        const verifyResponse = await verifyEmailAPI.verifyEmail({
            email: email,
            code: verificationCode
        });

        if (verifyResponse.status() !== 200) {
            const errorBody = await verifyResponse.text();
            console.error('Email verification failed:', errorBody);
            throw new Error(`Email verification failed with status ${verifyResponse.status()}`);
        }

        console.log('✅ Email verification successful!');
        const verifyBody = await verifyResponse.json();

        // The verify email API should return access token and refresh token
        if (verifyBody.accessToken) {
            console.log('✅ Got access token from verification response!');
            await use({
                email,
                password,
                accessToken: verifyBody.accessToken
            });
            return;
        }

        // If no access token in verify response, try login
        console.log('🔐 Attempting login after verification...');
        const finalLoginResponse = await loginAPI.login(email, password);

        if (finalLoginResponse.status() !== 200) {
            const errorBody = await finalLoginResponse.text();
            console.error('Login after verification failed:', errorBody);
            throw new Error(`Login failed with status ${finalLoginResponse.status()}`);
        }

        console.log('✅ Login successful after verification!');
        const loginBody = await finalLoginResponse.json();
        const accessToken = loginBody.data.accessToken;

        await use({
            email,
            password,
            accessToken
        });
    },

    applicationRequestsAPI: async ({ request, mentorCredentials }, use) => {
        // Use the existing request context but with authorization header
        const api = new ApplicationRequestsAPI(request);
        await use(api);
    }
});

test.describe('Application Requests API Tests', () => {

    test.describe('GET /api/application-requests/current-user', () => {
        test('AR_001 - Should get current user application request successfully', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: ApiResponseType;

            await test.step('Send GET request to get current user application', async () => {
                response = await applicationRequestsAPI.getCurrentUserApplicationRequest();
                responseBody = await response.json();
            });

            await test.step('Verify successful response', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                // Verify response structure if application exists
                if (responseBody.data) {
                    expect(responseBody.data).toHaveProperty('id');
                    expect(responseBody.data).toHaveProperty('education');
                    expect(responseBody.data).toHaveProperty('workExperience');
                    expect(responseBody.data).toHaveProperty('description');
                    expect(responseBody.data).toHaveProperty('status');
                }
            });
        });
    });

    test.describe('POST /api/application-requests', () => {
        test('AR_002 - Should create application request successfully with valid data', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: ApiResponseType;
            const requestData: CreateApplicationRequest = testData.createRequest.validData;

            await test.step('Send POST request to create application', async () => {
                response = await applicationRequestsAPI.createApplicationRequest(requestData);
                responseBody = await response.json();
            });

            await test.step('Verify successful creation', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.data).toBe("Application request created successfully");
                expect(responseBody.errors).toEqual([]);
            });
        });

        testData.createRequest.invalidData.forEach((testCase, index) => {
            test(`AR_003_${index + 1} - Should fail to create application: ${testCase.testCase}`, async ({ applicationRequestsAPI }) => {
                let response: any;
                let responseBody: ApiResponseType;

                await test.step(`Send POST request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.createApplicationRequest(testCase.data as CreateApplicationRequest);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    expect(responseBody.isSuccess).toBeFalsy();
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('PUT /api/application-requests', () => {
        test('AR_004 - Should update application request successfully with valid data', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: ApiResponseType;
            const updateData: UpdateApplicationRequest = testData.updateRequest.validData;

            await test.step('Send PUT request to update application', async () => {
                response = await applicationRequestsAPI.updateApplicationRequest(updateData);
                responseBody = await response.json();
            });

            await test.step('Verify successful update', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.data).toBe("Application request updated successfully");
                expect(responseBody.errors).toEqual([]);
            });
        });

        testData.updateRequest.invalidData.forEach((testCase, index) => {
            test(`AR_005_${index + 1} - Should fail to update application: ${testCase.testCase}`, async ({ applicationRequestsAPI }) => {
                let response: any;
                let responseBody: ApiResponseType;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.updateApplicationRequest(testCase.data as UpdateApplicationRequest);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    expect(responseBody.isSuccess).toBeFalsy();
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('GET /api/application-requests', () => {
        testData.getRequests.validParams.forEach((testCase, index) => {
            test(`AR_006_${index + 1} - Should get application requests: ${testCase.testCase}`, async ({ applicationRequestsAPI }) => {
                let response: any;
                let responseBody: ApiResponseType;

                await test.step(`Send GET request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.getApplicationRequests(testCase.params as GetApplicationRequestsParams);
                    responseBody = await response.json();
                });

                await test.step('Verify successful response', async () => {
                    expect(response.status()).toBe(200);
                    expect(responseBody.statusCode).toBe(200);
                    expect(responseBody.isSuccess).toBeTruthy();
                    expect(responseBody.data).toBeDefined();
                    expect(responseBody.data.items).toBeDefined();
                    expect(Array.isArray(responseBody.data.items)).toBeTruthy();
                    expect(responseBody.data.totalCount).toBeDefined();
                    expect(responseBody.data.pageNumber).toBe(testCase.params.PageNumber);
                    expect(responseBody.data.pageSize).toBe(testCase.params.PageSize);

                    // Verify item structure if items exist
                    if (responseBody.data.items.length > 0) {
                        const item = responseBody.data.items[0] as ApplicationRequestResponse;
                        expect(item).toHaveProperty('id');
                        expect(item).toHaveProperty('education');
                        expect(item).toHaveProperty('workExperience');
                        expect(item).toHaveProperty('fullName');
                        expect(item).toHaveProperty('description');
                        expect(item).toHaveProperty('status');
                        expect(item).toHaveProperty('summitted');
                    }
                });
            });
        });
    });

    test.describe('GET /api/application-requests/{id}', () => {
        test('AR_007 - Should get application request by ID successfully', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: ApiResponseType;
            const validId = testData.testIds.validId;

            await test.step('Send GET request to get application by ID', async () => {
                response = await applicationRequestsAPI.getApplicationRequestById(validId);
                responseBody = await response.json();
            });

            await test.step('Verify successful response', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.data).toBeDefined();

                const applicationDetail = responseBody.data as ApplicationRequestDetailResponse;
                expect(applicationDetail).toHaveProperty('id');
                expect(applicationDetail).toHaveProperty('education');
                expect(applicationDetail).toHaveProperty('workExperience');
                expect(applicationDetail).toHaveProperty('fullName');
                expect(applicationDetail).toHaveProperty('description');
                expect(applicationDetail).toHaveProperty('status');
                expect(applicationDetail).toHaveProperty('summitted');
                expect(applicationDetail).toHaveProperty('note');
                expect(applicationDetail).toHaveProperty('applicationRequestDocuments');
                expect(applicationDetail).toHaveProperty('mentorEmail');
                expect(applicationDetail).toHaveProperty('mentorExpertises');
                expect(applicationDetail).toHaveProperty('mentorCertifications');
                expect(applicationDetail).toHaveProperty('avatarUrl');
            });
        });

        test('AR_008 - Should return 404 for non-existent application ID', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: ApiResponseType;
            const invalidId = testData.testIds.invalidId;

            await test.step('Send GET request with invalid ID', async () => {
                response = await applicationRequestsAPI.getApplicationRequestById(invalidId);
                responseBody = await response.json();
            });

            await test.step('Verify 404 response', async () => {
                expect(response.status()).toBe(404);
                expect(responseBody.isSuccess).toBeFalsy();
                if (responseBody.errors && responseBody.errors.length > 0) {
                    expect(responseBody.errors[0].message).toContain('Application request not found');
                }
            });
        });
    });

    test.describe('PUT /api/application-requests/{id}/request-update', () => {
        test('AR_009 - Should request update successfully with valid data', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: ApiResponseType;
            const validId = testData.testIds.validId;
            const requestData = testData.requestUpdate.validData;

            await test.step('Send PUT request to request update', async () => {
                response = await applicationRequestsAPI.requestUpdate(validId, requestData);
                responseBody = await response.json();
            });

            await test.step('Verify successful request update', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.errors).toEqual([]);
            });
        });

        testData.requestUpdate.invalidData.forEach((testCase, index) => {
            test(`AR_010_${index + 1} - Should fail to request update: ${testCase.testCase}`, async ({ applicationRequestsAPI }) => {
                let response: any;
                let responseBody: any;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.requestUpdate(testCase.id, testCase.data);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('PUT /api/application-requests/{id}/approve', () => {
        test('AR_011 - Should approve application request successfully', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: any;
            const validId = testData.approveRequest.validId;

            await test.step('Send PUT request to approve application', async () => {
                response = await applicationRequestsAPI.approveRequest(validId);
                responseBody = await response.json();
            });

            await test.step('Verify successful approval', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.errors).toEqual([]);
            });
        });

        testData.approveRequest.invalidData.forEach((testCase, index) => {
            test(`AR_012_${index + 1} - Should fail to approve application: ${testCase.testCase}`, async ({ applicationRequestsAPI }) => {
                let response: any;
                let responseBody: any;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.approveRequest(testCase.id);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('PUT /api/application-requests/{id}/reject', () => {
        test('AR_013 - Should reject application request successfully with valid data', async ({ applicationRequestsAPI }) => {
            let response: any;
            let responseBody: any;
            const validId = testData.testIds.validId;
            const rejectData = testData.rejectRequest.validData;

            await test.step('Send PUT request to reject application', async () => {
                response = await applicationRequestsAPI.rejectRequest(validId, rejectData);
                responseBody = await response.json();
            });

            await test.step('Verify successful rejection', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody.statusCode).toBe(200);
                expect(responseBody.isSuccess).toBeTruthy();
                expect(responseBody.errors).toEqual([]);
            });
        });

        testData.rejectRequest.invalidData.forEach((testCase, index) => {
            test(`AR_014_${index + 1} - Should fail to reject application: ${testCase.testCase}`, async ({ applicationRequestsAPI }) => {
                let response: any;
                let responseBody: any;

                await test.step(`Send PUT request with ${testCase.testCase.toLowerCase()}`, async () => {
                    response = await applicationRequestsAPI.rejectRequest(testCase.id, testCase.data);
                    responseBody = await response.json();
                });

                await test.step('Verify error response', async () => {
                    expect(response.status()).toBe(testCase.expectedStatus);
                    if (responseBody.errors && responseBody.errors.length > 0) {
                        expect(responseBody.errors[0].message).toContain(testCase.expectedMessage);
                    }
                });
            });
        });
    });

    test.describe('Integration Tests', () => {
        test('AR_015 - Complete application lifecycle test', async ({ applicationRequestsAPI }) => {
            let createResponse: any;
            let createResponseBody: any;
            let applicationId: string;
            const createData: CreateApplicationRequest = testData.createRequest.validData;

            await test.step('Create new application request', async () => {
                createResponse = await applicationRequestsAPI.createApplicationRequest(createData);
                createResponseBody = await createResponse.json();
                expect(createResponse.status()).toBe(200);
                applicationId = createResponseBody.data?.id;
            });

            if (applicationId) {
                await test.step('Get created application by ID', async () => {
                    const getResponse = await applicationRequestsAPI.getApplicationRequestById(applicationId);
                    const getResponseBody = await getResponse.json();
                    expect(getResponse.status()).toBe(200);
                    expect(getResponseBody.data.id).toBe(applicationId);
                });

                await test.step('Update the application', async () => {
                    const updateData: UpdateApplicationRequest = {
                        ...testData.updateRequest.validData,
                        Id: applicationId
                    };
                    const updateResponse = await applicationRequestsAPI.updateApplicationRequest(updateData);
                    expect(updateResponse.status()).toBe(200);
                });

                await test.step('Request update from mentor', async () => {
                    const requestUpdateResponse = await applicationRequestsAPI.requestUpdate(
                        applicationId,
                        testData.requestUpdate.validData
                    );
                    expect(requestUpdateResponse.status()).toBe(200);
                });

                await test.step('Finally approve the application', async () => {
                    const approveResponse = await applicationRequestsAPI.approveRequest(applicationId);
                    expect(approveResponse.status()).toBe(200);
                });
            }
        });
    });
});