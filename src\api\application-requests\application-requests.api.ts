import { APIRequestContext, APIResponse } from '@playwright/test';
import { ENDPOINTS } from '../../core/const/endpoint';
import { 
    CreateApplicationRequest, 
    UpdateApplicationRequest,
    GetApplicationRequestsParams,
    RequestUpdateBody,
    RejectRequestBody
} from '../../data-type/application-request.type';

export default class ApplicationRequestsAPI {
    private request: APIRequestContext;

    constructor(request: APIRequestContext) {
        this.request = request;
    }

    async getCurrentUserApplicationRequest(): Promise<APIResponse> {
        return await this.request.get(ENDPOINTS.APPLICATION_REQUESTS_CURRENT_USER);
    }

    async createApplicationRequest(data: CreateApplicationRequest): Promise<APIResponse> {
        const multipartData: Record<string, any> = {
            Education: data.Education,
            WorkExperience: data.WorkExperience,
            Description: data.Description
        };

        // Handle certifications array
        if (data.Certifications && data.Certifications.length > 0) {
            multipartData.Certifications = data.Certifications;
        }

        if (data.Status !== undefined) {
            multipartData.Status = data.Status.toString();
        }

        // Handle application documents if provided
        if (data.ApplicationDocuments && data.ApplicationDocuments.length > 0) {
            multipartData.ApplicationDocuments = data.ApplicationDocuments;
        }

        return await this.request.post(ENDPOINTS.APPLICATION_REQUESTS, {
            multipart: multipartData
        });
    }

    async updateApplicationRequest(data: UpdateApplicationRequest): Promise<APIResponse> {
        const multipartData: Record<string, any> = {
            Id: data.Id,
            Education: data.Education,
            WorkExperience: data.WorkExperience,
            Description: data.Description
        };

        // Handle certifications array
        if (data.Certifications && data.Certifications.length > 0) {
            multipartData.Certifications = data.Certifications;
        }

        // Handle application documents if provided
        if (data.ApplicationDocuments && data.ApplicationDocuments.length > 0) {
            multipartData.ApplicationDocuments = data.ApplicationDocuments;
        }

        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS, {
            multipart: multipartData
        });
    }

    async getApplicationRequests(params: GetApplicationRequestsParams): Promise<APIResponse> {
        const queryParams: { [key: string]: string | number | boolean } = {
            PageSize: params.PageSize,
            PageNumber: params.PageNumber
        };
    
        if (params.Search) {
            queryParams.Search = params.Search;
        }
    
        if (params.ApplicationRequestStatuses && params.ApplicationRequestStatuses.length > 0) {
            // Convert array to comma-separated string or handle as multiple params
            queryParams.ApplicationRequestStatuses = params.ApplicationRequestStatuses.join(',');
        }
    
        return await this.request.get(ENDPOINTS.APPLICATION_REQUESTS, {
            params: queryParams
        });
    }

    async getApplicationRequestById(id: string): Promise<APIResponse> {
        return await this.request.get(ENDPOINTS.APPLICATION_REQUESTS_BY_ID(id));
    }

    async requestUpdate(id: string, data: RequestUpdateBody): Promise<APIResponse> {
        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS_REQUEST_UPDATE(id), {
            data: data
        });
    }

    async approveRequest(id: string): Promise<APIResponse> {
        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS_APPROVE(id));
    }

    async rejectRequest(id: string, data: RejectRequestBody): Promise<APIResponse> {
        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS_REJECT(id), {
            data: data
        });
    }
}